from pathlib import Path

import polars as pl

from core_silver.observation_converter.converters.converters_factory import (
    get_converter_class_using_source,
)
from data_sdk.domain.domain_types import ReportMetadataWithRawFile
from data_sdk.domain.source import Source
from data_sdk.domain.tables import ExternalFeatureFlagsTable, TableDefinition
from tests.__mocks__.tables.currencies import external_currency_exchange_rates_table
from tests.snapshot.metadata import all_test_metadata


class SnapshotGeneratorError(Exception):
    def __init__(self, param_name):
        super().__init__(f"Unsupported type for parameter {param_name}")


def main():
    for meta in all_test_metadata:
        parquet_path = get_expected_parquet_path(meta)
        if not parquet_path.exists():
            generate_parquet(meta)


def generate_parquet(meta):
    zip_content = read_zip_content(meta)
    if zip_content is None:
        return
    report_metadata = ReportMetadataWithRawFile(metadata=meta, raw_file=zip_content)
    converter_class = get_converter_class_using_source(Source(meta.source))
    init_kwargs = generate_init_kwargs(converter_class)
    converter = converter_class(report_metadata, **init_kwargs)
    result = converter.convert()
    write_parquet(meta, result.df)
    print(f"Generated: {get_expected_parquet_path(meta)}")


def generate_init_kwargs(cls):
    mocked_tables = get_mocked_tables()
    init_kwargs = {}
    for param_name, param_type in cls.__init__.__annotations__.items():
        if param_name == "return":
            continue
        if issubclass(param_type, TableDefinition):
            init_kwargs[param_name] = mocked_tables[param_name]
        else:
            if param_name == "raw_report":
                continue
            raise SnapshotGeneratorError(param_name)
    return init_kwargs


def get_mocked_tables():
    return {
        "external_currency_exchange_rates_table": external_currency_exchange_rates_table,
        "external_feature_flags_table": ExternalFeatureFlagsTable(
            df=pl.DataFrame({"name": [], "namespace": []})
        ),
    }


def read_zip_content(meta):
    zip_path = get_input_zip_path(meta)
    if not zip_path.exists():
        print(f"Input zip not found: {zip_path}")
        return None
    with zip_path.open("rb") as f:
        return f.read()


def write_parquet(meta, df):
    parquet_path = get_expected_parquet_path(meta)
    parquet_path.parent.mkdir(parents=True, exist_ok=True)

    # Convert datetime columns to string format to match converter output
    # This ensures snapshots match the actual converter behavior
    if "date" in df.columns:
        df = df.with_columns(pl.col("date").dt.strftime("%Y-%m-%d").alias("date"))

    df.write_parquet(parquet_path)


CORE_SILVER_ROOT_DIR = Path(__file__).parent.parent


def get_expected_parquet_path(meta):
    input_path = Path(meta.original_name)
    output_dir = CORE_SILVER_ROOT_DIR / "tests/snapshot/output"
    return output_dir / input_path.with_suffix(".parquet")


def get_input_zip_path(meta):
    input_path = Path(meta.original_name)
    input_dir = CORE_SILVER_ROOT_DIR / "tests/snapshot/input"
    return input_dir / input_path


if __name__ == "__main__":
    main()
