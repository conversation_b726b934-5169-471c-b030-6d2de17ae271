import pandas as pd


def compare_dataframes(actual: pd.DataFrame, expected: pd.DataFrame):
    """
    Compare two DataFrames and provide clean, readable diff report.
    """

    issues = []

    # Sort columns for consistent comparison
    actual = actual.sort_index(axis=1)
    expected = expected.sort_index(axis=1)

    actual_columns = set(actual.columns)
    expected_columns = set(expected.columns)
    common_columns = actual_columns & expected_columns

    # 1. COLUMN DIFFERENCES
    missing_in_actual = expected_columns - actual_columns
    extra_in_actual = actual_columns - expected_columns

    if missing_in_actual or extra_in_actual:
        column_issues = []
        if missing_in_actual:
            missing_str = ", ".join(sorted(missing_in_actual))
            column_issues.append(f"Missing in actual: {missing_str}")
        if extra_in_actual:
            extra_str = ", ".join(sorted(extra_in_actual))
            column_issues.append(f"Extra in actual: {extra_str}")
        issues.append("COLUMN MISMATCH: " + "; ".join(column_issues))

    # 2. DATA TYPE DIFFERENCES (group similar issues)
    dtype_issues = []
    object_to_category = []
    other_dtype_issues = []

    for col in common_columns:
        actual_type = str(actual[col].dtype)
        expected_type = str(expected[col].dtype)
        if actual_type != expected_type:
            if actual_type == "object" and expected_type == "category":
                object_to_category.append(col)
            else:
                other_dtype_issues.append(f"{col}: {actual_type} → {expected_type}")

    if object_to_category:
        dtype_issues.append(
            f"Object→Category conversion needed: {', '.join(object_to_category[:5])}"
            + (
                f" (+{len(object_to_category) - 5} more)"
                if len(object_to_category) > 5
                else ""
            )
        )

    if other_dtype_issues:
        for issue in other_dtype_issues[:3]:  # Show first 3 significant type issues
            dtype_issues.append(f"Type mismatch: {issue}")
        if len(other_dtype_issues) > 3:
            dtype_issues.append(
                f"... and {len(other_dtype_issues) - 3} more type mismatches"
            )

    if dtype_issues:
        issues.append("DATA TYPES: " + "; ".join(dtype_issues))

    # 3. ROW COUNT
    actual_rows = len(actual)
    expected_rows = len(expected)

    if actual_rows != expected_rows:
        diff = actual_rows - expected_rows
        issues.append(
            f"ROW COUNT: actual={actual_rows}, expected={expected_rows} (difference: {diff:+d})"
        )

    # 4. DATA CONTENT DIFFERENCES
    if common_columns and min(actual_rows, expected_rows) > 0:
        try:
            min_rows = min(actual_rows, expected_rows)
            actual_subset = actual[list(common_columns)].iloc[:min_rows].copy()
            expected_subset = expected[list(common_columns)].iloc[:min_rows].copy()

            # Try sorting for better comparison
            try:
                actual_sorted = actual_subset.sort_values(
                    by=list(common_columns)
                ).reset_index(drop=True)
                expected_sorted = expected_subset.sort_values(
                    by=list(common_columns)
                ).reset_index(drop=True)
            except:
                actual_sorted = actual_subset.reset_index(drop=True)
                expected_sorted = expected_subset.reset_index(drop=True)

            # Find rows that are different
            different_rows = []
            examples = []

            for idx in range(min_rows):
                row_diffs = []
                for col in common_columns:
                    val_actual = actual_sorted.iloc[idx][col]
                    val_expected = expected_sorted.iloc[idx][col]

                    # Handle NaN comparison
                    if pd.isna(val_actual) and pd.isna(val_expected):
                        continue
                    elif val_actual != val_expected:
                        row_diffs.append((col, val_actual, val_expected))

                if row_diffs:
                    different_rows.append(idx)
                    if len(examples) < 3:  # Show only first 3 examples
                        examples.append((
                            idx,
                            row_diffs[:3],
                        ))  # Max 3 column diffs per example

            if different_rows:
                issues.append(
                    f"DATA CONTENT: {len(different_rows)}/{min_rows} rows have differences"
                )

                # Add specific examples
                example_details = []
                for row_idx, row_diffs in examples:
                    diff_desc = []
                    for col, actual_val, expected_val in row_diffs:
                        # Format values nicely
                        actual_str = str(actual_val)[:50]
                        expected_str = str(expected_val)[:50]
                        diff_desc.append(f"{col}: '{actual_str}' vs '{expected_str}'")

                    example_details.append(f"Row {row_idx}: " + "; ".join(diff_desc))

                if example_details:
                    issues.append("EXAMPLES: " + " | ".join(example_details))

                # Column-level statistics
                column_diff_counts = {}
                for idx in different_rows:
                    for col in common_columns:
                        val_actual = actual_sorted.iloc[idx][col]
                        val_expected = expected_sorted.iloc[idx][col]

                        if pd.isna(val_actual) and pd.isna(val_expected):
                            continue
                        elif val_actual != val_expected:
                            column_diff_counts[col] = column_diff_counts.get(col, 0) + 1

                if column_diff_counts:
                    # Show top 5 columns with most differences
                    sorted_cols = sorted(
                        column_diff_counts.items(), key=lambda x: x[1], reverse=True
                    )
                    top_cols = [f"{col}({count})" for col, count in sorted_cols[:5]]
                    issues.append(f"MOST AFFECTED COLUMNS: {', '.join(top_cols)}")

        except Exception as e:
            issues.append(f"DATA COMPARISON ERROR: {str(e)}")

    # 5. Save debug files
    try:
        expected.to_parquet("expected.parquet")
        actual.to_parquet("actual.parquet")
    except Exception:
        pass  # Ignore save errors

    # 6. FINAL ASSERTION
    if issues:
        error_message = f"DataFrames differ in {len(issues)} way(s):\n\n"
        for i, issue in enumerate(issues, 1):
            error_message += f"{i}. {issue}\n"

        error_message += f"\nShapes: actual={actual.shape}, expected={expected.shape}"
        error_message += "\nDebug files: expected.parquet, actual.parquet"

        raise AssertionError(error_message)
